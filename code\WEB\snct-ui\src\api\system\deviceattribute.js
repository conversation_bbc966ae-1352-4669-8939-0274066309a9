import request from '@/utils/request'

// 查询设备属性列表
export function listDeviceattribute(query) {
  return request({
    url: '/business/deviceattribute/list',
    method: 'get',
    params: query
  })
}

// 查询设备属性详细
export function getDeviceattribute(id) {
  return request({
    url: '/business/deviceattribute/' + id,
    method: 'get'
  })
}

// 新增设备属性
export function addDeviceattribute(data) {
  return request({
    url: '/business/deviceattribute',
    method: 'post',
    data: data
  })
}

// 修改设备属性
export function updateDeviceattribute(data) {
  return request({
    url: '/business/deviceattribute',
    method: 'put',
    data: data
  })
}

// 删除设备属性
export function delDeviceattribute(id) {
  return request({
    url: '/business/deviceattribute/' + id,
    method: 'delete'
  })
}
