import request from '@/utils/request'

// 查询pdu消息列表
export function listPdu(query) {
  return request({
    url: '/devicemsg/pdu/list',
    method: 'get',
    params: query
  })
}

// 查询pdu消息详细
export function getPdu(id) {
  return request({
    url: '/devicemsg/pdu/' + id,
    method: 'get'
  })
}

// 新增pdu消息
export function addPdu(data) {
  return request({
    url: '/devicemsg/pdu',
    method: 'post',
    data: data
  })
}

// 修改pdu消息
export function updatePdu(data) {
  return request({
    url: '/devicemsg/pdu',
    method: 'put',
    data: data
  })
}

// 删除pdu消息
export function delPdu(id) {
  return request({
    url: '/devicemsg/pdu/' + id,
    method: 'delete'
  })
}

// ================================================================
// 从HBase中获取PDU数据的接口
// ================================================================

// 分页查询PDU消息列表
export function listPduHbase(query) {
  return request({
    url: '/hbase/devicemsg/pdu/query',
    method: 'get',
    params: query
  })
}

// 查询PDU消息详细
export function getPduHbase(rowKey) {
  return request({
    url: '/hbase/devicemsg/pdu/detail/' + rowKey,
    method: 'get'
  })
}

// 查询PDU设备通道数据详细
export function getPduChannels(rowKey) {
  return request({
    url: '/hbase/devicemsg/pdu/channels/' + rowKey,
    method: 'get'
  })
}
