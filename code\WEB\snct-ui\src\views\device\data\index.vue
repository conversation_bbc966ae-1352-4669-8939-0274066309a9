<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="部门" prop="deptId">
        <treeselect
          v-model="queryParams.deptId"
          :options="deptOptions"
          :normalizer="normalizer"
          :show-count="true"
          placeholder="请选择部门"
          @change="handleDeptIdChange"
          style="width: 200px"
          clearable
        />
      </el-form-item>
      <el-form-item label="船舶" prop="sn">
        <el-select
          v-model="queryParams.sn"
          :placeholder="queryParams.deptId ? '请选择船舶' : '请先选择部门'"
          clearable
          style="width: 200px"
          @change="handleShipChange"
          :disabled="!queryParams.deptId || shipOptions.length === 0"
        >
          <el-option
            v-for="ship in shipOptions"
            :key="ship.id"
            :label="ship.name"
            :value="ship.sn"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="设备" prop="deviceId">
        <el-select
          v-model="queryParams.deviceId"
          :placeholder="queryParams.sn ? '请选择设备' : '请先选择船舶'"
          clearable
          style="width: 200px"
          @change="handleDeviceChange"
          :disabled="!queryParams.sn || deviceOptions.length === 0"
        >
          <el-option
            v-for="device in deviceOptions"
            :key="device.id"
            :label="device.name + '(' + device.code + ')'"
            :value="device.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="时间范围" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd HH:mm:ss"
          style="width: 300px"
          align="right"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="数据间隔" prop="interval">
        <el-select v-model="queryParams.interval" placeholder="请选择数据间隔" style="width: 120px">
          <el-option label="100ms" :value="100"></el-option>
          <el-option label="500ms" :value="500"></el-option>
          <el-option label="1秒" :value="1000"></el-option>
          <el-option label="5秒" :value="5000"></el-option>
          <el-option label="10秒" :value="10000"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="排序" prop="sortOrder">
        <el-radio-group v-model="queryParams.sortOrder" size="mini">
          <el-radio-button label="desc">倒序</el-radio-button>
          <el-radio-button label="asc">正序</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-view"
          size="mini"
          :disabled="!selectedDevice"
          @click="handleViewDetails"
        >查看详情</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="resetPagination"></right-toolbar>
    </el-row>

    <!-- 设备信息显示 -->
    <el-alert
      v-if="selectedDevice"
      :title="`当前设备：${selectedDevice.name} (${selectedDevice.code}) - ${getDeviceTypeName(selectedDevice.type)}`"
      type="info"
      :closable="false"
      style="margin-bottom: 10px"
    ></el-alert>

    <!-- 动态数据表格 -->
    <el-table
      v-loading="loading"
      :data="dataList"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
      highlight-current-row
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="70" align="center">
        <template slot-scope="scope">
          <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}</span>
        </template>
      </el-table-column>

      <!-- 动态生成的数据列 -->
      <el-table-column
        v-for="column in dynamicColumns"
        :key="column.prop"
        :label="column.label"
        :prop="column.prop"
        :width="column.width"
        :min-width="column.minWidth"
        align="center"
      >
        <template slot-scope="scope">
          <span>{{ formatCellValue(scope.row[column.prop], column) }}</span>
        </template>
      </el-table-column>

      <!-- 固定的时间列 -->
      <el-table-column label="采集时间" align="center" prop="timestamp" width="160" fixed="right">
        <template slot-scope="scope">
          <span>{{ formatTimestamp(scope.row.timestamp) }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 详情对话框 -->
    <el-dialog title="设备数据详情" :visible.sync="detailDialogVisible" width="80%" :close-on-click-modal="false">
      <el-descriptions :column="2" border>
        <el-descriptions-item
          v-for="(value, key) in selectedRowData"
          :key="key"
          :label="getFieldLabel(key)"
        >
          {{ formatDetailValue(value, key) }}
        </el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDeviceData, getDeviceDetailsByRowKeys } from '@/api/device/data'
import { deptTreeSelect } from '@/api/system/user'
import { listShipByDeptId } from '@/api/system/ship'
import { listDevice } from '@/api/system/device'
import { listDeviceattribute } from '@/api/system/deviceattribute'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  name: 'DeviceData',
  components: { Treeselect },
  dicts: ['device_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      selectedRowKeys: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 日期范围
      dateRange: [],
      // 设备数据列表
      dataList: [],
      // 部门树选项
      deptOptions: [],
      // 船只选项
      shipOptions: [],
      // 设备选项
      deviceOptions: [],
      // 当前选中的设备
      selectedDevice: null,
      // 设备属性配置
      deviceAttributes: [],
      // 动态列配置
      dynamicColumns: [],
      // 详情对话框
      detailDialogVisible: false,
      selectedRowData: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deptId: null,
        sn: null,
        deviceId: null,
        startTime: null,
        endTime: null,
        interval: 100,
        sortOrder: 'desc'
      }
    }
  },
  created() {
    this.getDeptTree()
  },
  watch: {
    'queryParams.deptId': {
      handler(newVal) {
        if (newVal) {
          this.getShipList(newVal)
        } else {
          this.shipOptions = []
          this.queryParams.sn = null
        }
      },
      immediate: false
    },
    'queryParams.sn': {
      handler(newVal) {
        if (newVal) {
          this.getDeviceList(newVal)
        } else {
          this.deviceOptions = []
          this.queryParams.deviceId = null
        }
      },
      immediate: false
    },
    'queryParams.deviceId': {
      handler(newVal) {
        if (newVal) {
          this.selectedDevice = this.deviceOptions.find(d => d.id === newVal)
          if (this.selectedDevice) {
            this.loadDeviceAttributes(this.selectedDevice.type)
          }
        } else {
          this.selectedDevice = null
          this.dynamicColumns = []
        }
      },
      immediate: false
    }
  },
  methods: {
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.label,
        children: node.children
      }
    },

    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then(response => {
        this.deptOptions = response.data
      })
    },

    /** 处理部门ID变更 */
    handleDeptIdChange(val) {
      if (!val) {
        this.queryParams.sn = null
        this.shipOptions = []
        this.$nextTick(() => {
          this.$message.info('请先选择部门再选择船舶')
        })
      } else {
        this.getShipList(val)
      }
    },

    /** 获取部门下的船只列表 */
    getShipList(deptId) {
      listShipByDeptId(deptId).then(response => {
        if (response.code === 200) {
          this.shipOptions = response.data || []
          this.queryParams.sn = null
        } else {
          this.shipOptions = []
        }
      }).catch(() => {
        this.shipOptions = []
      })
    },

    /** 处理船舶变更 */
    handleShipChange(val) {
      this.queryParams.deviceId = null
      this.deviceOptions = []
    },

    /** 获取设备列表 */
    getDeviceList(sn) {
      const params = { sn: sn }
      listDevice(params).then(response => {
        if (response.code === 200) {
          this.deviceOptions = response.rows || response.data || []
          if (this.deviceOptions.length === 0) {
            this.$message.info('未找到该船舶下的设备')
          }
        } else {
          this.deviceOptions = []
          this.$message.warning('获取设备列表失败: ' + (response.msg || '未知错误'))
        }
      }).catch(() => {
        this.deviceOptions = []
        this.$message.error('获取设备列表出错')
      })
    },

    /** 处理设备变更 */
    handleDeviceChange(val) {
      // 设备选择变更时的处理逻辑
    },

    /** 加载设备属性配置 */
    loadDeviceAttributes(deviceType) {
      const params = { type: deviceType }
      listDeviceattribute(params).then(response => {
        if (response.code === 200) {
          this.deviceAttributes = response.rows || response.data || []
          this.generateDynamicColumns()
        } else {
          this.deviceAttributes = []
          this.dynamicColumns = []
        }
      }).catch(() => {
        this.deviceAttributes = []
        this.dynamicColumns = []
      })
    },

    /** 生成动态列配置 */
    generateDynamicColumns() {
      this.dynamicColumns = this.deviceAttributes.map(attr => ({
        prop: attr.name,
        label: attr.label || attr.name,
        width: this.getColumnWidth(attr.name),
        minWidth: 100,
        type: attr.type || 'string'
      }))
    },

    /** 获取列宽度 */
    getColumnWidth(fieldName) {
      const widthMap = {
        'timestamp': 160,
        'time': 160,
        'latitude': 120,
        'longitude': 120,
        'speed': 100,
        'course': 100,
        'temperature': 120,
        'humidity': 100,
        'pressure': 120
      }
      return widthMap[fieldName] || 120
    },

    /** 获取设备类型名称 */
    getDeviceTypeName(type) {
      const deviceType = this.dict.type.device_type.find(item => item.value == type)
      return deviceType ? deviceType.label : '未知类型'
    },

    /** 格式化单元格值 */
    formatCellValue(value, column) {
      if (value === null || value === undefined || value === '') {
        return 'N/A'
      }

      // 根据字段类型进行格式化
      switch (column.type) {
        case 'number':
          return typeof value === 'number' ? value.toFixed(2) : value
        case 'boolean':
          return value ? '是' : '否'
        case 'timestamp':
          return this.formatTimestamp(value)
        default:
          return value
      }
    },

    /** 格式化时间戳 */
    formatTimestamp(timestamp) {
      if (!timestamp) return 'N/A'

      // 如果是时间戳数字，转换为日期
      if (typeof timestamp === 'number') {
        return new Date(timestamp).toLocaleString('zh-CN')
      }

      // 如果已经是格式化的字符串，直接返回
      return timestamp
    },

    /** 获取字段标签 */
    getFieldLabel(fieldName) {
      const attr = this.deviceAttributes.find(a => a.name === fieldName)
      return attr ? attr.label : fieldName
    },

    /** 格式化详情值 */
    formatDetailValue(value, key) {
      if (value === null || value === undefined || value === '') {
        return 'N/A'
      }

      if (key === 'timestamp' || key.includes('time')) {
        return this.formatTimestamp(value)
      }

      return value
    },

    /** 查询设备数据列表 */
    getList() {
      if (!this.queryParams.deviceId) {
        this.$message.warning('请先选择设备')
        return
      }

      this.loading = true

      // 处理日期时间范围
      const params = { ...this.queryParams }
      if (this.dateRange && this.dateRange.length === 2) {
        params.startTime = new Date(this.dateRange[0]).getTime()
        params.endTime = new Date(this.dateRange[1]).getTime()
      }

      listDeviceData(params).then(response => {
        this.dataList = response.rows || response.data || []
        this.total = response.total || 0
        this.loading = false
      }).catch(() => {
        this.dataList = []
        this.total = 0
        this.loading = false
        this.$message.error('查询设备数据失败')
      })
    },

    /** 重置分页回到第一页 */
    resetPagination() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.queryParams.pageNum = 1
      this.dataList = []
      this.total = 0
    },

    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id || item.rowKey)
      this.selectedRowKeys = selection.map(item => item.rowKey).filter(key => key)
    },

    /** 行点击事件 */
    handleRowClick(row) {
      this.selectedRowData = row
    },

    /** 查看详情 */
    handleViewDetails() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请先选择要查看的数据行')
        return
      }

      const params = {
        deviceId: this.queryParams.deviceId,
        interval: this.queryParams.interval,
        rowkeys: this.selectedRowKeys.join(',')
      }

      getDeviceDetailsByRowKeys(params).then(response => {
        if (response.code === 200 && response.data && response.data.length > 0) {
          this.selectedRowData = response.data[0]
          this.detailDialogVisible = true
        } else {
          this.$message.warning('未找到详细数据')
        }
      }).catch(() => {
        this.$message.error('获取详细数据失败')
      })
    }
  }
}
</script>

<style scoped>
.el-alert {
  margin-bottom: 10px;
}

.vue-treeselect >>> .vue-treeselect__value-container {
  height: 30px;
  display: block;
}

.vue-treeselect >>> .vue-treeselect__control {
  height: 30px;
}

.dialog-footer {
  text-align: center;
}
</style>