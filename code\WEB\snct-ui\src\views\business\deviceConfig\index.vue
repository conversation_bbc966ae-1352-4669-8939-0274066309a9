<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="81px">
      <el-form-item label="部门名称" prop="deptName">
        <el-input
          v-model="queryParams.deptName"
          placeholder="请输入船舶名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      
      <el-form-item label="船舶名称" prop="shipName">
        <el-input
          v-model="queryParams.shipName"
          placeholder="请输入船舶名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备名称" prop="deviceName">
        <el-input
          v-model="queryParams.deviceName"
          placeholder="请选择设备名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!--
      <el-form-item label="配置名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入配置名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="配置类型//在代码里面配置" prop="configKey">
        <el-input
          v-model="queryParams.configKey"
          placeholder="请输入配置类型//在代码里面配置"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      -->
      <!--
      <el-form-item label="设备编号" prop="configValue">
        <el-input
          v-model="queryParams.configValue"
          placeholder="请输入设备编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="启用状态" prop="enable">
        <el-input
          v-model="queryParams.enable"
          placeholder="请输入启用状态"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['business:deviceConfig:add']"
        >新增</el-button>
      </el-col>
      <!--
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['business:deviceConfig:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['business:deviceConfig:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['business:deviceConfig:export']"
        >导出</el-button>
      </el-col>
      -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="deviceConfigList" @selection-change="handleSelectionChange">
      <!--
      <el-table-column type="selection" width="55" align="center" />
      -->
      <el-table-column label="序号" type="index" width="70" align="center">
        <template slot-scope="scope">
          <span>{{(queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1}}</span>
        </template>
      </el-table-column>
      <el-table-column label="企业/部门" align="center" prop="deptName" />
      <el-table-column label="船舶名称" align="center" prop="shipName" />
      <el-table-column label="设备名称" align="center" prop="deviceName" />
      <!--
      <el-table-column label="配置类型" align="center" prop="name" />
      -->
      <el-table-column label="配置项" align="center" prop="dictLabel" />
      <el-table-column label="配置值" align="center" prop="configValue" />
      <el-table-column label="启用状态" align="center" prop="enable" />

      <el-table-column label="状态" align="center" prop="enable">
        <template slot-scope="scope">
          <el-tag :type="scope.row.enable === 1 ? 'success' : 'danger'">
            {{ scope.row.enable === 1 ? "正常" : "未启用" }}
          </el-tag>
        </template>
      </el-table-column>
      <!--
      <el-table-column label="备注" align="center" prop="remark" />
      -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['business:deviceConfig:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['business:deviceConfig:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改设备配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="419px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="88px">
        <!--
        <el-form-item label="企业/部门" prop="deptId">
          <el-input v-model="form.deptId" placeholder="请输入部门ID" />
        </el-form-item>
        <el-form-item label="船名" prop="sn">
          <el-input v-model="form.sn" placeholder="请输入船sn" />
        </el-form-item>
        -->
        <el-form-item label="企业/部门" prop="deptName"  v-show="this.vdept === 1"    >
          <treeselect  style="width:240px"
            v-model="form.deptId"
            :options="deptOptions"
            :normalizer="normalizer"
            @input="getship"
            placeholder="选择企业/部门"
          />
        </el-form-item>

        <el-form-item label="船舶名称" prop="sn" v-show="this.vdept === 1" >
          <el-select v-model="form.sn" placeholder="请选择船舶名称" style="width:240px" clearable @input="getdevice" >
              <el-option v-for="item in ships" :key="item.sn" :label="item.name" :value="item.sn" />
            </el-select>
        </el-form-item>

        <el-form-item label="设备名称" prop="deviceId" v-show="this.vdept === 1" >
          <el-select v-model="form.deviceId" placeholder="请选择设备名称" style="width:240px" clearable  @input="getdeviceparam"  >
              <el-option v-for="item in devices" :key="item.deviceId" :label="item.name" :value="item.deviceId" />
            </el-select>
        </el-form-item>

        <el-form-item label="配置项" prop="configKey" v-show="this.vdept === 1" >
          <el-select v-model="form.configKey" placeholder="请选择配置项" style="width:240px" clearable>
              <el-option v-for="item in deviceparams" :key="item.configKey" :label="item.name" :value="item.configKey" />
            </el-select>
        </el-form-item>
        <el-form-item label="企业/部门" prop="dictLabel" v-show="this.vdept === 0" >
          <div>{{ form.deptName }}</div>
        </el-form-item>
        <el-form-item label="船舶名称" prop="dictLabel" v-show="this.vdept === 0" >
          <div>{{ form.shipName }}</div>
        </el-form-item>
        <el-form-item label="设备名称" prop="dictLabel" v-show="this.vdept === 0" >
          <div>{{ form.deviceName }}</div>
        </el-form-item>
        <el-form-item label="配置项" prop="dictLabel" v-show="this.vdept === 0" >
          <div>{{ form.dictLabel }}</div>
        </el-form-item>
        <!--
        <el-form-item label="设备名称" prop="deviceId">
          <el-input v-model="form.deviceId" placeholder="请输入设备ID" style="width:240px" />
        </el-form-item>
        
        <el-form-item label="配置项" prop="name">
          <el-input v-model="form.name" placeholder="请输入配置名称" />
        </el-form-item>
        
        <el-form-item label="配置项" prop="configKey">
          <el-input v-model="form.configKey" placeholder="请输入配置值" style="width:240px" />
        </el-form-item>
        -->
        <el-form-item label="配置值" prop="configValue">
          <el-input v-model="form.configValue" placeholder="请输入配置值" style="width:240px" />
        </el-form-item>

        <el-form-item label="启用状态" prop="enable" style="margin-bottom:12px;margin-top:-9px;">
            <el-switch v-model="form.enable" :active-value="1" :inactive-value="0"></el-switch>
        </el-form-item>
        <!--
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" style="width:240px" />
        </el-form-item>
        -->
      </el-form>
      <div slot="footer" style="margin-top: -27px;" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDeviceConfig, getDeviceConfig, delDeviceConfig, addDeviceConfig,updateDeviceConfig,listDeptExcludeChild,getShipList,listDept,getDeviceList,getDeviceparamList } from "@/api/system/deviceConfig";
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  name: "DeviceConfig",
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      vdept: 1,    //1部门显示 其他隐藏
      ships: [
        //{ sn: 1, name: 'xxx1' },
        //{ sn: 3, name: 'xxx3' }
      ],
      devices: [
        //{ deviceId: 1, name: 'xxex1' },
        //{ deviceId: 3, name: 'xxrx3' }
      ],
      deviceparams: [
        //{ configKey: 'K19', name: 'xxqx1' },
        //{ configKey: 'K22', name: 'xxrx2' }
      ],
      // 单位/部门树选项
      deptOptions: [],
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备配置表格数据
      deviceConfigList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceId: null,
        deptId: null,
        sn: null,
        name: null,
        configKey: null,
        configValue: null,
        enable: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        deviceId: [
          { required: true, message: "设备ID不能为空", trigger: "blur" }
        ],
        deptId: [
          { required: true, message: "部门ID不能为空", trigger: "blur" }
        ],
        sn: [
          { required: true, message: "船sn不能为空", trigger: "blur" }
        ],
        name: [
          { required: true, message: "配置名称不能为空", trigger: "blur" }
        ],
        configKey: [
          { required: true, message: "配置类型//在代码里面配置不能为空", trigger: "blur" }
        ],
        configValue: [
          { required: true, message: "设备编号不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询设备配置列表 */
    getList() {
      this.loading = true;
      listDeviceConfig(this.queryParams).then(response => {
        this.deviceConfigList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getship(){
      //alert(this.selectedValue);
      getShipList(this.form.deptId).then(response => {
        console.log(response.data);
        const jsonArray = JSON.stringify(response.data);
        this.ships = response.data;
        this.devices = "";
        if(this.vdept===1){
          this.form.sn="";
          this.form.deviceId="";
        }
      });
    },
    getdevice(){
      getDeviceList(this.form.sn).then(response => {
        console.log(response.data);
        const jsonArray = JSON.stringify(response.data);
        this.devices = response.data;
        this.form.deviceId="";
        if(this.vdept===1){
        //  this.form.sn="";
           this.form.deviceId="";
        }
      });
    },
    getdeviceparam(){
      getDeviceparamList().then(response => {
        console.log(response.data);
        const jsonArray = JSON.stringify(response.data);
        this.deviceparams = response.data;
        //if(this.vdept===1){
        //  this.form.sn="";
        //}
      });
    },
    /** 转换单位/部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        deviceId: null,
        deptId: null,
        sn: null,
        name: null,
        configKey: null,
        configValue: null,
        enable: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加设备配置";
      this.vdept=1;
      listDept().then(response => {
        this.deptOptions = this.handleTree(response.data, 'deptId')
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getDeviceConfig(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改设备配置";
        this.vdept=0;
        listDeptExcludeChild(row.deptId).then(response => {
          this.deptOptions = this.handleTree(response.data, 'deptId')
          //console.log(JSON.stringify(response.data));
          if (this.deptOptions.length === 0) {
            const noResultsOptions = { deptId: this.form.parentId, deptName: this.form.parentName, children: [] }
            this.deptOptions.push(noResultsOptions)
          }
        })
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDeviceConfig(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDeviceConfig(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除设备配置数据项？').then(function() {
        return delDeviceConfig(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('business/deviceConfig/export', {
        ...this.queryParams
      }, `deviceConfig_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
