import request from '@/utils/request'

// ================================================================
// 从HBase中获取姿态仪数据的接口
// ================================================================

// 分页查询姿态仪消息列表
export function listAttitudeHbase(query) {
  return request({
    url: '/hbase/devicemsg/attitude/query',
    method: 'get',
    params: query
  })
}

// 查询姿态仪消息详细
export function getAttitudeHbase(rowKey) {
  return request({
    url: '/hbase/devicemsg/attitude/detail/' + rowKey,
    method: 'get'
  })
}
