<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="73px">
      <!--
      <el-form-item label="部门ID" prop="deptId">
        <el-input
          v-model="queryParams.deptId"
          placeholder="请输入部门ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      -->
      <el-form-item label="部门名称" prop="deptName">
        <el-input
          v-model="queryParams.deptName"
          placeholder="请输入船舶名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      
      <el-form-item label="船舶名称" prop="shipName">
        <el-input
          v-model="queryParams.shipName"
          placeholder="请输入船舶名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="船长" prop="captain">
        <el-input
          v-model="queryParams.captain"
          placeholder="请输入船长"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="航次编号" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入航次编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!--
      <el-form-item label="航次开始时间" prop="startTime">
        <el-input
          v-model="queryParams.startTime"
          placeholder="请输入航次开始时间"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="航次结束时间" prop="finishTime">
        <el-input
          v-model="queryParams.finishTime"
          placeholder="请输入航次结束时间"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="总天数" prop="totalDays">
        <el-input
          v-model="queryParams.totalDays"
          placeholder="请输入总天数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="历史里程" prop="historyMileage">
        <el-input
          v-model="queryParams.historyMileage"
          placeholder="请输入历史里程"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="起始地" prop="startPort">
        <el-input
          v-model="queryParams.startPort"
          placeholder="请输入起始地"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="目的地" prop="endPort">
        <el-input
          v-model="queryParams.endPort"
          placeholder="请输入目的地"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="目标海域" prop="seaArea">
        <el-input
          v-model="queryParams.seaArea"
          placeholder="请输入目标海域"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['business:cruise:add']"
        >新增</el-button>
      </el-col>
      <!--
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['business:cruise:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['business:cruise:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['business:cruise:export']"
        >导出</el-button>
      </el-col>
      -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="cruiseList" @selection-change="handleSelectionChange">
      <!--
      <el-table-column type="selection" width="55" align="center" />
      -->
      <el-table-column label="序号" type="index" width="70" align="center">
        <template slot-scope="scope">
          <span>{{(queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1}}</span>
        </template>
      </el-table-column>
      <el-table-column label="企业/部门" align="center" prop="deptName" width="179px"/>
      <el-table-column label="船舶名称" align="center" prop="shipName" width="154px"/>
      <el-table-column label="船长" align="center" prop="captain" width="93px"/>
      <el-table-column label="航次编号" align="center" prop="code" width="136px"/>
      <el-table-column label="开始时间" align="center" prop="startTime" width="168px"/>
      <el-table-column label="结束时间" align="center" prop="finishTime" width="168px"/>
      <el-table-column label="总天数" align="center" prop="totalDays" />
      <el-table-column label="历史里程" align="center" prop="historyMileage" />
      <el-table-column label="起始地" align="center" prop="startPort" />
      <el-table-column label="目的地" align="center" prop="endPort" />
      <!--
      <el-table-column label="目标海域" align="center" prop="seaArea" />
      -->
      <!--
      <el-table-column label="属性json" align="center" prop="moduleJson" />
      -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['business:cruise:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['business:cruise:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改航次对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <!--
        <el-form-item label="部门ID" prop="deptId">
          <el-input v-model="form.deptId" placeholder="请输入部门ID" style="width:240px" />
        </el-form-item>
        -->

        <el-form-item label="企业/部门" prop="deptName"  v-show="this.vdept === 1"    >
          <treeselect  style="width:240px"
            v-model="form.deptId"
            :options="deptOptions"
            :normalizer="normalizer"
            @input="getship"
            placeholder="选择企业/部门"
          />
        </el-form-item>

        <el-form-item label="船舶名称" prop="sn" v-show="this.vdept === 1" >
          <el-select v-model="form.sn" placeholder="请选择" style="width:240px" clearable>
              <el-option v-for="item in ships" :key="item.sn" :label="item.name" :value="item.sn" />
            </el-select>
        </el-form-item>

        <el-form-item label="企业/部门" prop="captain" v-show="this.vdept === 0" >
          <div>{{ form.deptName }}</div>
        </el-form-item>
        <el-form-item label="船舶名称" prop="captain" v-show="this.vdept === 0" >
          <div>{{ form.shipName }}</div>
        </el-form-item>


        <el-form-item label="船长" prop="captain">
          <el-input v-model="form.captain" placeholder="请输入船长" style="width:240px" />
        </el-form-item>
        <el-form-item label="航次编号" prop="code">
          <el-input v-model="form.code" placeholder="请输入航次编号" style="width:240px" />
        </el-form-item>
        <!--
        <el-form-item label="航次开始时间" prop="startTime">
          <el-input v-model="form.startTime" placeholder="请输入航次开始时间"  style="width:240px"/>
        </el-form-item>
        <el-form-item label="航次结束时间" prop="finishTime">
          <el-input v-model="form.finishTime" placeholder="请输入航次结束时间"  style="width:240px"/>
        </el-form-item>
        -->
        <el-form-item label="航次开始时间" prop="startTime">
          <el-date-picker
            v-model="form.startTime"
            type="datetime"
            format="yyyy-MM-DD HH:mm:ss"
            value-format="yyyy-MM-DD HH:mm:ss"
            placeholder="请选择航次开始时间"
            style="width: 240px;"
          />
        </el-form-item>

        <el-form-item label="航次结束时间" prop="finishTime">
          <el-date-picker
            v-model="form.finishTime"
            type="datetime"
            format="yyyy-MM-DD HH:mm:ss"
            value-format="yyyy-MM-DD HH:mm:ss"
            placeholder="请选择航次结束时间"
            style="width: 240px;"
          />
        </el-form-item>

        <el-form-item label="总天数" prop="totalDays">
          <el-input v-model="form.totalDays" placeholder="请输入总天数"  style="width:240px"/>
        </el-form-item>
        <el-form-item label="历史里程" prop="historyMileage">
          <el-input v-model="form.historyMileage" placeholder="请输入历史里程" style="width:240px" />
        </el-form-item>
        <el-form-item label="起始地" prop="startPort">
          <el-input v-model="form.startPort" placeholder="请输入起始地" style="width:240px"/>
        </el-form-item>
        <el-form-item label="目的地" prop="endPort">
          <el-input v-model="form.endPort" placeholder="请输入目的地" style="width:240px"/>
        </el-form-item>
        <el-form-item label="目标海域" prop="seaArea">
          <el-input v-model="form.seaArea" placeholder="请输入目标海域" style="width:240px"/>
        </el-form-item>
        <!--
        <el-form-item label="属性json" prop="moduleJson">
          <el-input v-model="form.moduleJson" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        -->
      </el-form>
      <div style="margin-top: -27px;" slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCruise, getCruise, delCruise, addCruise, updateCruise,listDept,listDeptExcludeChild,getShipList } from "@/api/system/cruise";
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  name: "Cruise",
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      vdept: 1,    //1部门显示 其他隐藏
      ships: [
        //{ sn: 1, name: 'xxx1' },
        //{ sn: 2, name: 'xxx2' },
        //{ sn: 3, name: 'xxx3' }
      ],
      // 单位/部门树选项
      deptOptions: [],
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 航次表格数据
      cruiseList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deptId: null,
        sn: null,
        captain: null,
        code: null,
        startTime: null,
        finishTime: null,
        totalDays: null,
        historyMileage: null,
        startPort: null,
        endPort: null,
        seaArea: null,
        moduleJson: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        code: [
          { required: true, message: "航次编号不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询航次列表 */
    getList() {
      this.loading = true;
      listCruise(this.queryParams).then(response => {
        this.cruiseList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getship(){
      //alert(this.selectedValue);
      getShipList(this.form.deptId).then(response => {
        console.log(response.data);
        const jsonArray = JSON.stringify(response.data);
        this.ships = response.data;
        if(this.vdept===1){
          this.form.sn="";
        }
      });
    },
    /** 转换单位/部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        cruiseId: null,
        deptId: null,
        sn: null,
        captain: null,
        code: null,
        startTime: null,
        finishTime: null,
        totalDays: null,
        historyMileage: null,
        startPort: null,
        endPort: null,
        seaArea: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        moduleJson: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.cruiseId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加航次";
      this.vdept=1;
      listDept().then(response => {
        this.deptOptions = this.handleTree(response.data, 'deptId')
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const cruiseId = row.cruiseId || this.ids
      getCruise(cruiseId).then(response => {
        //alert(this.form.deptId);
        //this.getship();
        this.form = response.data;
        this.open = true;
        this.title = "修改航次";
        this.vdept=0;
        listDeptExcludeChild(row.deptId).then(response => {
          this.deptOptions = this.handleTree(response.data, 'deptId')
          //console.log(JSON.stringify(response.data));
          if (this.deptOptions.length === 0) {
            const noResultsOptions = { deptId: this.form.parentId, deptName: this.form.parentName, children: [] }
            this.deptOptions.push(noResultsOptions)
          }
        })
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {

          if (this.form.cruiseId != null) {
            updateCruise(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCruise(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const cruiseIds = row.cruiseId || this.ids;
      this.$modal.confirm('是否确认删除航次数据项？').then(function() {
        return delCruise(cruiseIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('business/cruise/export', {
        ...this.queryParams
      }, `cruise_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
