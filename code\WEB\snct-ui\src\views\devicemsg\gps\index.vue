<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="部门" prop="deptId">
        <treeselect
          v-model="queryParams.deptId"
          :options="deptOptions"
          :normalizer="normalizer"
          :show-count="true"
          placeholder="请选择部门"
          @change="handleDeptIdChange"
          style="width: 200px"
          clearable
        />
      </el-form-item>
      <el-form-item label="船舶" prop="sn">
        <el-select
          v-model="queryParams.sn"
          :placeholder="queryParams.deptId ? '请选择船舶' : '请先选择部门'"
          clearable
          style="width: 200px"
          @change="handleShipChange"
          :disabled="!queryParams.deptId || shipOptions.length === 0"
        >
          <el-option
            v-for="ship in shipOptions"
            :key="ship.id"
            :label="ship.name"
            :value="ship.sn"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="设备" prop="deviceId">
        <el-select
          v-model="queryParams.deviceId"
          :placeholder="queryParams.deptId ? '请选择设备' : '请先选择部门'"
          clearable
          style="width: 200px"
          @change="handleDeviceChange"
          :disabled="!queryParams.deptId || deviceOptions.length === 0"
        >
          <el-option
            v-for="device in deviceOptions"
            :key="device.daDeviceId"
            :label="device.name + '(' + device.code + ')'"
            :value="device.daDeviceId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="时间" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          style="width: 200px"
          align="right"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="排序" prop="sortOrder">
        <el-radio-group v-model="queryParams.sortOrder" size="mini">
          <el-radio-button label="desc">倒序</el-radio-button>
          <el-radio-button label="asc">正序</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="resetPagination"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="gpsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="90" align="center">
        <template slot-scope="scope">
          <span>{{scope.$index + 1}}</span>
        </template>
      </el-table-column>
      <el-table-column label="单位/部门" align="center" prop="deptName">
        <template slot-scope="scope">
          <span>{{ scope.row.deptName || 'N/A' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="船舶" align="center" prop="shipName">
        <template slot-scope="scope">
          <span>{{ scope.row.shipName || 'N/A' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="设备" align="center" prop="deviceName" />
      <el-table-column label="纬度" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.latitude && scope.row.latitudeHemisphere ? `${scope.row.latitude} ${scope.row.latitudeHemisphere}` : 'N/A' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="经度" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.longitude && scope.row.longitudeHemisphere ? `${scope.row.longitude} ${scope.row.longitudeHemisphere}` : 'N/A' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="地面航向(°)" align="center" prop="groundCourse">
        <template slot-scope="scope">
          <span>{{ scope.row.groundCourse || 'N/A' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="地面速率(M/s)" align="center" prop="groundRate">
        <template slot-scope="scope">
          <span>{{ scope.row.groundRate || 'N/A' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="卫星数量" align="center" prop="satelliteId">
        <template slot-scope="scope">
          <span>{{ scope.row.satelliteId || 'N/A' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="定位状态" align="center" prop="position">
        <template slot-scope="scope">
          <span>
            {{ 
              !scope.row.position ? 'N/A' : 
              scope.row.position.toUpperCase() === 'A' ? '有效' : 
              scope.row.position.toUpperCase() === 'V' ? '无效' : 
              scope.row.position 
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="采集时间" align="center" prop="initialBjTime" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.initialBjTime || 'N/A' }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listGpsHbase } from '@/api/devicemsg/gps'
import { deptTreeSelect } from '@/api/system/user'
import { listShipByDeptId } from '@/api/system/ship'
import { listDeviceBySnAndType } from '@/api/system/device'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  name: 'GpsHbase',
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 日期范围
      dateRange: [],
      // GPS消息表格数据
      gpsList: [],
      // 部门树选项
      deptOptions: [],
      // 船只选项
      shipOptions: [],
      // 设备选项
      deviceOptions: [],
      // 设备类型 - GPS
      deviceType: 32,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deptId: null,
        sn: null,
        deviceId: null,
        sortOrder: 'desc'
      },
      // 树形结构配置选项
      defaultProps: {
        children: 'children',
        label: 'label'
      }
    }
  },
  created() {
    this.getDeptTree()
    this.getList()
  },
  watch: {
    'queryParams.deptId': {
      handler(newVal, oldVal) {
        if (newVal) {
          this.getShipList(newVal)
        } else {
          this.shipOptions = []
          this.queryParams.sn = null
        }
      },
      immediate: false
    },
    'queryParams.sn': {
      handler(newVal, oldVal) {
        if (newVal) {
          // 船舶SN有值时获取设备列表
          this.getDeviceList(newVal)
        } else {
          // 清空船舶SN时，清空设备列表和已选设备
          this.deviceOptions = []
          this.queryParams.deviceId = null
        }
      },
      immediate: false
    }
  },
  methods: {
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.label,
        children: node.children
      }
    },

    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then(response => {
        this.deptOptions = response.data
      })
    },

    /** 处理部门ID变更 */
    handleDeptIdChange(val) {
      // 如果清空部门ID，也同时清空船舶SN和船只选项
      if (!val) {
        this.queryParams.sn = null
        this.shipOptions = []
        this.$nextTick(() => {
          this.$message.info('请先选择部门再选择船舶')
        })
      } else {
        this.getShipList(val)
      }
    },

    /** 获取部门下的船只列表 */
    getShipList(deptId) {
      listShipByDeptId(deptId).then(response => {
        if (response.code === 200) {
          this.shipOptions = response.data || []
          // 清空之前选择的SN
          this.queryParams.sn = null
        } else {
          this.shipOptions = []
        }
      }).catch(() => {
        this.shipOptions = []
      })
    },

    /** 校验查询参数 */
    validateQueryParams() {
      // 如果设置了船舶SN但没有部门ID
      if (this.queryParams.sn && !this.queryParams.deptId) {
        this.$message.warning('查询船舶数据时需要先指定部门ID')
        return false
      }
      return true
    },

    /** 查询GPS消息列表 */
    getList() {
      // 先校验查询参数
      if (!this.validateQueryParams()) {
        return
      }

      this.loading = true

      // 处理日期时间范围
      const params = { ...this.queryParams }
      if (this.dateRange && this.dateRange.length === 2) {
        params.startDateTime = this.dateRange[0]
        params.endDateTime = this.dateRange[1]
      }

      listGpsHbase(params).then(response => {
        this.gpsList = response.rows
        this.total = response.total
        this.loading = false
      }).catch(() => {
        this.gpsList = []
        this.total = 0
        this.loading = false
      })
    },

    /** 重置分页回到第一页 */
    resetPagination() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.queryParams.pageNum = 1
      this.getList()
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },

    /** 处理船舶变更 */
    handleShipChange(val) {
      // 清空设备ID和设备选项
      this.queryParams.deviceId = null
      this.deviceOptions = []
    },

    /** 获取设备列表 */
    getDeviceList(sn) {
      listDeviceBySnAndType(sn, this.deviceType).then(response => {
        if (response.code === 200) {
          this.deviceOptions = response.rows || response.data || []
          if (this.deviceOptions.length === 0) {
            this.$message.info('未找到该船舶下的GPS设备')
          }
        } else {
          this.deviceOptions = []
          this.$message.warning('获取设备列表失败: ' + (response.msg || '未知错误'))
        }
      }).catch(() => {
        this.deviceOptions = []
        this.$message.error('获取设备列表出错')
      })
    },

    /** 处理设备变更 */
    handleDeviceChange(val) {
      // 设备选择变更时的处理逻辑
    }
  }
}
</script>

<style scoped>
.pagination-container {
  margin-top: 15px;
  text-align: center;
}
.el-form-item__tip {
  margin-top: 5px;
  line-height: 1;
}

.vue-treeselect >>> .vue-treeselect__value-container {
  height: 30px;
  display: block;
}

.vue-treeselect >>> .vue-treeselect__control {
  height: 30px;
}
</style>
