import request from '@/utils/request'

// 查询串口配置列表
export function listSerialConfig(query) {
  return request({
    url: '/business/serialConfig/list',
    method: 'get',
    params: query
  })
}

// 查询串口配置详细
export function getSerialConfig(id) {
  return request({
    url: '/business/serialConfig/' + id,
    method: 'get'
  })
}

// 新增串口配置
export function addSerialConfig(data) {
  return request({
    url: '/business/serialConfig',
    method: 'post',
    data: data
  })
}

// 修改串口配置
export function updateSerialConfig(data) {
  return request({
    url: '/business/serialConfig',
    method: 'put',
    data: data
  })
}

// 删除串口配置
export function delSerialConfig(id) {
  return request({
    url: '/business/serialConfig/' + id,
    method: 'delete'
  })
}
