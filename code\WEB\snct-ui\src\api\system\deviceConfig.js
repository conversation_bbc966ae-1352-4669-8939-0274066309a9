import request from '@/utils/request'

// 查询设备配置列表
export function listDeviceConfig(query) {
  return request({
    url: '/business/deviceConfig/list',
    method: 'get',
    params: query
  })
}

// 查询设备配置详细
export function getDeviceConfig(id) {
  return request({
    url: '/business/deviceConfig/' + id,
    method: 'get'
  })
}

// 新增设备配置
export function addDeviceConfig(data) {
  return request({
    url: '/business/deviceConfig',
    method: 'post',
    data: data
  })
}

// 修改设备配置
export function updateDeviceConfig(data) {
  return request({
    url: '/business/deviceConfig',
    method: 'put',
    data: data
  })
}

// 删除设备配置
export function delDeviceConfig(id) {
  return request({
    url: '/business/deviceConfig/' + id,
    method: 'delete'
  })
}

// 查询部门列表
export function listDept(query) {
  return request({
    url: '/system/dept/list',
    method: 'get',
    params: query
  })
}

// 查询部门列表（排除节点）
export function listDeptExcludeChild(deptId) {
  return request({
    url: '/system/dept/list/exclude/' + deptId,
    method: 'get'
  })
}

// 查询船只列表
export function getShipList(deptId) {
  return request({
    url: '/business/ship/listBy/'+deptId,
    method: 'get'
  })
}

// 查询设备列表
export function getDeviceList(sn) {
  if(sn==""){
    sn = null;
  }
  return request({
    url: '/business/device/listBy/'+sn,
    method: 'get'
  })
}

// 查询船只列表
export function getDeviceparamList() {
  return request({
    url: '/business/device/paramList',
    method: 'get'
  })
}
