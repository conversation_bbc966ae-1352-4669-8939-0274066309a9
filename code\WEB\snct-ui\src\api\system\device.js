import request from '@/utils/request'

// 查询设备列表
export function listDevice(query) {
  return request({
    url: '/business/device/list',
    method: 'get',
    params: query
  })
}

// 查询设备详细
export function getDevice(id) {
  return request({
    url: '/business/device/' + id,
    method: 'get'
  })
}

// 新增设备
export function addDevice(data) {
  return request({
    url: '/business/device',
    method: 'post',
    data: data
  })
}

// 修改设备
export function updateDevice(data) {
  return request({
    url: '/business/device',
    method: 'put',
    data: data
  })
}

// 删除设备
export function delDevice(id) {
  return request({
    url: '/business/device/' + id,
    method: 'delete'
  })
}

// 同步设备信息
export function syncDevice() {
  return request({
    url: '/business/device/sync',
    method: 'post'
  })
}

// 刷新设备通信信息
export function refData(deviceid,code) {
  return request({
    url: '/business/device/refreshdata?deviceid='+deviceid+"&code="+code,
    method: 'get'
  })
}

// 刷新设备通信信息
export function refreshdataclose() {
  return request({
    url: '/business/device/refreshdataclose',
    method: 'get'
  })
}

// 启用设备
export function enableDevice(id) {
  return request({
    url: '/business/device/enable/' + id,
    method: 'post'
  })
}

// 禁用设备
export function disableDevice(id) {
  return request({
    url: '/business/device/disable/' + id,
    method: 'post'
  })
}
