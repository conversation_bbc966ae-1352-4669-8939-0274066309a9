<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="部门" prop="deptId">
        <treeselect
          v-model="queryParams.deptId"
          :options="deptOptions"
          :normalizer="normalizer"
          :show-count="true"
          placeholder="请选择部门"
          @change="handleDeptIdChange"
          style="width: 200px"
          clearable
        />
      </el-form-item>
      <el-form-item label="船舶" prop="sn">
        <el-select
          v-model="queryParams.sn"
          :placeholder="queryParams.deptId ? '请选择船舶' : '请先选择部门'"
          clearable
          style="width: 200px"
          @change="handleShipChange"
          :disabled="!queryParams.deptId || shipOptions.length === 0"
        >
          <el-option
            v-for="ship in shipOptions"
            :key="ship.id"
            :label="ship.name"
            :value="ship.sn"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="设备" prop="deviceId">
        <el-select
          v-model="queryParams.deviceId"
          :placeholder="queryParams.deptId ? '请选择设备' : '请先选择部门'"
          clearable
          style="width: 200px"
          @change="handleDeviceChange"
          :disabled="!queryParams.deptId || deviceOptions.length === 0"
        >
          <el-option
            v-for="device in deviceOptions"
            :key="device.daDeviceId"
            :label="device.name + '(' + device.code + ')'"
            :value="device.daDeviceId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="时间" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          style="width: 200px"
          align="right"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="排序" prop="sortOrder">
        <el-radio-group v-model="queryParams.sortOrder" size="mini">
          <el-radio-button label="desc">倒序</el-radio-button>
          <el-radio-button label="asc">正序</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="resetPagination"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="attitudeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="90" align="center">
        <template slot-scope="scope">
          <span>{{scope.$index + 1}}</span>
        </template>
      </el-table-column>
      <el-table-column label="单位/部门" align="center" prop="deptName">
        <template slot-scope="scope">
          <span>{{ scope.row.deptName || 'N/A' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="船舶" align="center" prop="shipName">
        <template slot-scope="scope">
          <span>{{ scope.row.shipName || 'N/A' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="设备" align="center" prop="deviceName" />
      <el-table-column label="纬度" align="center" prop="lat">
        <template slot-scope="scope">
          <span>{{ scope.row.lat != null && scope.row.lat !== '' ? parseFloat(scope.row.lat).toFixed(6) : 'N/A' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="经度" align="center" prop="lon">
        <template slot-scope="scope">
          <span>{{ scope.row.lon != null && scope.row.lon !== '' ? parseFloat(scope.row.lon).toFixed(6) : 'N/A' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="航向(°)" align="center" prop="heading">
        <template slot-scope="scope">
          <span>{{ scope.row.heading != null && scope.row.heading !== '' ? parseFloat(scope.row.heading).toFixed(2) : 'N/A' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="俯仰(°)" align="center" prop="pitch">
        <template slot-scope="scope">
          <span>{{ scope.row.pitch != null && scope.row.pitch !== '' ? parseFloat(scope.row.pitch).toFixed(2) : 'N/A' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="横滚(°)" align="center" prop="rolling">
        <template slot-scope="scope">
          <span>{{ scope.row.rolling != null && scope.row.rolling !== '' ? parseFloat(scope.row.rolling).toFixed(2) : 'N/A' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="高度(m)" align="center" prop="elpHeight">
        <template slot-scope="scope">
          <span>{{ scope.row.elpHeight != null && scope.row.elpHeight !== '' ? parseFloat(scope.row.elpHeight).toFixed(2) : 'N/A' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="北向速度(m/s)" align="center" prop="velN">
        <template slot-scope="scope">
          <span>{{ scope.row.velN != null && scope.row.velN !== '' ? parseFloat(scope.row.velN).toFixed(3) : 'N/A' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="东向速度(m/s)" align="center" prop="velE">
        <template slot-scope="scope">
          <span>{{ scope.row.velE != null && scope.row.velE !== '' ? parseFloat(scope.row.velE).toFixed(3) : 'N/A' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="垂直速度(m/s)" align="center" prop="velD">
        <template slot-scope="scope">
          <span>{{ scope.row.velD != null && scope.row.velD !== '' ? parseFloat(scope.row.velD).toFixed(3) : 'N/A' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="地面速度(m/s)" align="center" prop="velG">
        <template slot-scope="scope">
          <span>{{ scope.row.velG != null && scope.row.velG !== '' ? parseFloat(scope.row.velG).toFixed(3) : 'N/A' }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="位置指示" align="center" prop="positionIndicator">
        <template slot-scope="scope">
          <span>{{ scope.row.positionIndicator || 'N/A' }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="航向指示" align="center" prop="headingIndicator">
        <template slot-scope="scope">
          <span>{{ scope.row.headingIndicator || 'N/A' }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="卫星数" align="center" prop="svn">
        <template slot-scope="scope">
          <span>{{ scope.row.svn != null && scope.row.svn !== '' ? parseInt(scope.row.svn) : 'N/A' }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="差分龄期" align="center" prop="diffAge">
        <template slot-scope="scope">
          <span>{{ scope.row.diffAge != null && scope.row.diffAge !== '' ? parseFloat(scope.row.diffAge).toFixed(1) : 'N/A' }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="基线长度(m)" align="center" prop="baselineLength">
        <template slot-scope="scope">
          <span>{{ scope.row.baselineLength != null && scope.row.baselineLength !== '' ? parseFloat(scope.row.baselineLength).toFixed(3) : 'N/A' }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="解算卫星数" align="center" prop="solutionSv">
        <template slot-scope="scope">
          <span>{{ scope.row.solutionSv != null && scope.row.solutionSv !== '' ? parseInt(scope.row.solutionSv) : 'N/A' }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="采集时间" align="center" prop="initialBjTime" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.initialBjTime || 'N/A' }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listAttitudeHbase } from '@/api/devicemsg/attitude'
import { deptTreeSelect } from '@/api/system/user'
import { listShipByDeptId } from '@/api/system/ship'
import { listDeviceBySnAndType } from '@/api/system/device'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  name: 'AttitudeHbase',
  components: { Treeselect },
  data() {
    return {
      loading: true,
      ids: [],
      single: true,
      multiple: true,
      showSearch: true,
      total: 0,
      dateRange: [],
      attitudeList: [],
      deptOptions: [],
      shipOptions: [],
      deviceOptions: [],
      deviceType: 33,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deptId: null,
        sn: null,
        deviceId: null,
        sortOrder: 'desc'
      },
      defaultProps: {
        children: 'children',
        label: 'label'
      }
    }
  },
  created() {
    this.getDeptTree()
    this.getList()
  },
  watch: {
    'queryParams.deptId': {
      handler(newVal) {
        if (newVal) {
          this.getShipList(newVal)
        } else {
          this.shipOptions = []
          this.queryParams.sn = null
        }
      },
      immediate: false
    },
    'queryParams.sn': {
      handler(newVal) {
        if (newVal) {
          this.getDeviceList(newVal)
        } else {
          this.deviceOptions = []
          this.queryParams.deviceId = null
        }
      },
      immediate: false
    }
  },
  methods: {
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.label,
        children: node.children
      }
    },

    getDeptTree() {
      deptTreeSelect().then(response => {
        this.deptOptions = response.data
      })
    },

    handleDeptIdChange(val) {
      if (!val) {
        this.queryParams.sn = null
        this.shipOptions = []
        this.$nextTick(() => {
          this.$message.info('请先选择部门再选择船舶')
        })
      } else {
        this.getShipList(val)
      }
    },

    getShipList(deptId) {
      listShipByDeptId(deptId).then(response => {
        if (response.code === 200) {
          this.shipOptions = response.data || []
          this.queryParams.sn = null
        } else {
          this.shipOptions = []
        }
      }).catch(() => {
        this.shipOptions = []
      })
    },

    validateQueryParams() {
      if (this.queryParams.sn && !this.queryParams.deptId) {
        this.$message.warning('查询船舶数据时需要先指定部门ID')
        return false
      }
      return true
    },

    getList() {
      if (!this.validateQueryParams()) {
        return
      }

      this.loading = true

      const params = { ...this.queryParams }
      if (this.dateRange && this.dateRange.length === 2) {
        params.startDateTime = this.dateRange[0]
        params.endDateTime = this.dateRange[1]
      }

      listAttitudeHbase(params).then(response => {
        this.attitudeList = response.rows
        this.total = response.total
        this.loading = false
      }).catch(() => {
        this.attitudeList = []
        this.total = 0
        this.loading = false
      })
    },

    resetPagination() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.queryParams.pageNum = 1
      this.getList()
    },

    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },

    handleShipChange() {
      this.queryParams.deviceId = null
      this.deviceOptions = []
    },

    getDeviceList(sn) {
      listDeviceBySnAndType(sn, this.deviceType).then(response => {
        if (response.code === 200) {
          this.deviceOptions = response.rows || response.data || []
          if (this.deviceOptions.length === 0) {
            this.$message.info('未找到该船舶下的姿态仪设备')
          }
        } else {
          this.deviceOptions = []
          this.$message.warning('获取设备列表失败: ' + (response.msg || '未知错误'))
        }
      }).catch(() => {
        this.deviceOptions = []
        this.$message.error('获取设备列表出错')
      })
    },

    handleDeviceChange() {
      // 设备选择变更处理
    }
  }
}
</script>

<style scoped>
.pagination-container {
  margin-top: 15px;
  text-align: center;
}
.el-form-item__tip {
  margin-top: 5px;
  line-height: 1;
}

.vue-treeselect >>> .vue-treeselect__value-container {
  height: 30px;
  display: block;
}

.vue-treeselect >>> .vue-treeselect__control {
  height: 30px;
}
</style>