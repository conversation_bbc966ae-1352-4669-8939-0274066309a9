<template>
  <div class="app-container">


    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="81px">
      <el-form-item label="企业/部门" prop="deptName">
        <el-input v-model="queryParams.deptName" placeholder="请输入企业/部门" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="船舶名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入船舶名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>


    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['business:ship:add']">新增</el-button>
      </el-col>
      <!--
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-upload"
          size="mini"
          @click="handleSync"
          v-hasPermi="['business:ship:edit']"
        >同步</el-button>
      </el-col>
      -->
      <!--
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['business:ship:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['business:ship:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['business:ship:export']">导出</el-button>
      </el-col>
      -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="shipList" @selection-change="handleSelectionChange">
      <!--
      <el-table-column type="selection" width="55" align="center" />
      -->
      <el-table-column label="序号" type="index" width="70" align="center">
        <template slot-scope="scope">
          <span>{{(queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1}}</span>
        </template>
      </el-table-column>
      <!--
      <el-table-column label="序号" align="center" prop="shipId" />
      <el-table-column label="部门" align="center" prop="deptName" />
      -->
      <el-table-column label="企业/部门" align="center" prop="deptName" />
      <el-table-column label="船舶名称" align="center" prop="name" />
      <el-table-column label="SN" align="center" prop="sn" />
      <el-table-column label="MMSI" align="center" prop="mmsi" />
      <el-table-column label="IMO" align="center" prop="imo" />
      <el-table-column label="呼号" align="center" prop="callSign" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
            {{ scope.row.status === 1 ? "正常" : "未启用" }}
          </el-tag>
        </template>
      </el-table-column>
      <!-- <el-table-column label="属性json" align="center" prop="moduleJson" /> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['business:ship:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['business:ship:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改船舶对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="420px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">

        <el-form-item label="企业/部门" prop="deptName"  v-show="this.vdept === 1"    >
          <treeselect  style="width:240px"
            v-model="form.deptId"
            :options="deptOptions"
            :normalizer="normalizer"
            placeholder="选择企业/部门"
          />
        </el-form-item>

        <el-form-item label="企业/部门" prop="deptName"  v-show="this.vdept === 0"    >
          <div>{{ form.deptName }}</div>
        </el-form-item>


        <el-form-item label="船名" prop="name">
          <el-input v-model="form.name" placeholder="请输入船名" style="width:240px"/>
        </el-form-item>
        <el-form-item label="SN" prop="sn">
          <el-input v-model="form.sn" placeholder="请输入SN" style="width:240px"/>
        </el-form-item>
        <!--
        <el-form-item label="状态" prop="status" style="margin-bottom:20px;">
          <el-select v-model="form.status" placeholder="请选状态" style="width:240px">
            <el-option v-for="dict in dict.type.ship_status" :key="dict.value" :label="dict.label"
              :value="parseInt(dict.value)" />
          </el-select>
        </el-form-item>
        -->
        <el-form-item label="状态" prop="status" style="margin-bottom:12px;margin-top:-9px;">
            <el-switch v-model="form.status" :active-value="1" :inactive-value="0"></el-switch>
        </el-form-item>

        <el-form-item label="MMSI" prop="mmsi">
          <el-input v-model="form.mmsi" placeholder="请输入MMSI" style="width:240px"/>
        </el-form-item>
        <el-form-item label="呼号" prop="callSign">
          <el-input v-model="form.callSign" placeholder="请输入呼号" style="width:240px"/>
        </el-form-item>
        <el-form-item label="IMO" prop="imo">
          <el-input v-model="form.imo" placeholder="请输入IMO" style="width:240px"/>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" style="width:240px"/>
        </el-form-item>
        <!--
        <el-form-item label="属性json" prop="moduleJson">
          <el-input v-model="form.moduleJson" type="textarea" placeholder="请输入内容" style="width:240px"/>
        </el-form-item>
        -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listShip,
  getShip,
  delShip,
  addShip,
  updateShip,
  syncShip,listDept,listDeptExcludeChild
} from "@/api/system/ship";
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  name: "Ship",
  dicts: ["ship_status"],
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      vdept: 1,    //1部门显示 其他隐藏
      // 单位/部门树选项
      deptOptions: [],
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 船舶表格数据
      shipList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deptId: null,
        sn: null,
        name: null,
        mmsi: null,
        callSign: null,
        imo: null,
        status: null,
        moduleJson: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        deptCode: [
          { required: true, message: "编号不能为空", trigger: "blur" },
        ],
        sn: [{ required: true, message: "SN不能为空", trigger: "blur" }],
        name: [{ required: true, message: "船名不能为空", trigger: "blur" }],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询船舶列表 */
    getList() {
      this.loading = true;
      listShip(this.queryParams).then((response) => {
        this.shipList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 转换单位/部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children
      }
    },
    // 表单重置
    reset() {
      this.form = {
        shipId: null,
        deptId: null,
        sn: null,
        name: null,
        mmsi: null,
        callSign: null,
        imo: null,
        status: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        moduleJson: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.shipId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加船舶";
      this.vdept=1;
      listDept().then(response => {
        this.deptOptions = this.handleTree(response.data, 'deptId')
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const shipId = row.shipId || this.ids;
      getShip(shipId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改船舶";
        this.vdept=0;
        listDeptExcludeChild(row.deptId).then(response => {
          this.deptOptions = this.handleTree(response.data, 'deptId')
          //console.log(JSON.stringify(response.data));
          if (this.deptOptions.length === 0) {
            const noResultsOptions = { deptId: this.form.parentId, deptName: this.form.parentName, children: [] }
            this.deptOptions.push(noResultsOptions)
          }
        })
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.shipId != null) {
            updateShip(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addShip(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const shipIds = row.shipId || this.ids;
      this.$modal
        .confirm('是否确认删除船舶数据项？')
        .then(function () {
          return delShip(shipIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "business/ship/export",
        {
          ...this.queryParams,
        },
        `ship_${new Date().getTime()}.xlsx`
      );
    },
    /** 同步船舶信息 */
    handleSync() {
      this.$modal.confirm('确认同步船舶信息吗？').then(() => {
        this.loading = true;
        syncShip().then(response => {
          this.$modal.msgSuccess(response.msg);
          this.loading = false;
        }).catch(() => {
          this.loading = false;
        });
      }).catch(() => {});
    },
  },
};
</script>
