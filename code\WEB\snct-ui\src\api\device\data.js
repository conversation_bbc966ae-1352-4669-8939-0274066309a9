import request from '@/utils/request'

/**
 * 分页查询设备数据列表
 * @param {Object} query 查询参数
 * @param {number} query.deviceId 设备ID
 * @param {number} query.startTime 开始时间戳
 * @param {number} query.endTime 结束时间戳
 * @param {number} query.interval 数据间隔
 * @param {number} query.pageNum 页码，默认1
 * @param {number} query.pageSize 每页记录数，默认10
 * @param {string} query.sortOrder 排序方式，默认desc
 */
export function listDeviceData(query) {
  return request({
    url: '/business/hbase/data/list',
    method: 'get',
    params: query
  })
}

/**
 * 根据rowkey查询设备详细数据
 * @param {Object} params 查询参数
 * @param {number} params.deviceId 设备ID（必填）
 * @param {number} params.interval 数据间隔，默认100
 * @param {string} params.rowkeys rowkey列表，多个用逗号分隔（必填）
 */
export function getDeviceDetailsByRowKeys(params) {
  return request({
    url: '/business/hbase/data/details',
    method: 'get',
    params: params
  })
}