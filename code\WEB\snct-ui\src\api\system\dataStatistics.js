import request from '@/utils/request'

// 查询采集-传输记录列表
export function listDataStatistics(query) {
  return request({
    url: '/business/dataStatistics/list',
    method: 'get',
    params: query
  })
}

// 查询采集-传输记录详细
export function getDataStatistics(id) {
  return request({
    url: '/business/dataStatistics/' + id,
    method: 'get'
  })
}

// 新增采集-传输记录
export function addDataStatistics(data) {
  return request({
    url: '/business/dataStatistics',
    method: 'post',
    data: data
  })
}

// 修改采集-传输记录
export function updateDataStatistics(data) {
  return request({
    url: '/business/dataStatistics',
    method: 'put',
    data: data
  })
}

// 删除采集-传输记录
export function delDataStatistics(id) {
  return request({
    url: '/business/dataStatistics/' + id,
    method: 'delete'
  })
}
