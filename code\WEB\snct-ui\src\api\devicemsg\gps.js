import request from '@/utils/request'

// ================================================================
// 从HBase中获取GPS数据的接口
// ================================================================

// 分页查询GPS消息列表
export function listGpsHbase(query) {
  return request({
    url: '/hbase/devicemsg/gps/query',
    method: 'get',
    params: query
  })
}

// 查询GPS消息详细
export function getGpsHbase(rowKey) {
  return request({
    url: '/hbase/devicemsg/gps/detail/' + rowKey,
    method: 'get'
  })
}
