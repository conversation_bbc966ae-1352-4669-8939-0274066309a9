<template>
  <div class="app-container">
      <el-form ref="form" :model="form" label-width="150px">
        <el-form-item label="北斗传输开启" >
          <el-radio-group v-model="bdselectedOption">
            <el-radio
              v-for="dict in dict.type.sys_yes_no"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="网络传输开启" >
          <el-radio-group v-model="netselectedOption">
            <el-radio
              v-for="dict in dict.type.sys_yes_no"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>

      </el-form>
      <div slot="footer" style="padding-left: 110px;padding-top: 9px;" class="dialog-footer">
        <el-button type="primary" @click="updateInfo">设置</el-button>
        <!--
        <el-button type="danger" @click="restart">重启生效</el-button>
        -->
      </div>
  </div>
</template>

<script>
import { setingInfo, updatesetingInfo,restart} from "@/api/system/seting";

export default {
  name: "Seting",
  dicts: ['sys_yes_no'],
  data() {
    return {
      bdselectedOption: 'Y', // 初始值设为空字符串或其他初始值
      netselectedOption: 'N', // 初始值设为空字符串或其他初始值
      // 表单参数
      form: {},
    };
  },
  created() {
    this.getInfo();
  },
  methods: {
    /** 查询参数列表 */
    getInfo() {
        setingInfo().then(response => {
           //alert(response.data.bd_transfer_function);
           this.bdselectedOption=response.data.bd_transfer_function;
           this.netselectedOption=response.data.net_transfer_function;
        }
      );
    },
    updateInfo() {
        var obj = {
          'bdselectedOption':this.bdselectedOption,
          'netselectedOption':this.netselectedOption
        };
        updatesetingInfo(obj).then(response => {
          alert(response.msg);
        }
      );
    },
    restart() {
        restart().then(response => {
          //重启服务
        }
      );
    }
  }
}
</script>
