<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true">
      <el-form-item label="单位/部门" prop="deptName">
        <el-input v-model="queryParams.deptName" placeholder="请输入单位/部门名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:dept:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-sort" size="mini" @click="toggleExpandAll">展开/折叠</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-if="refreshTable"
      v-loading="loading"
      :data="deptList"
      row-key="deptId"
      :default-expand-all="isExpandAll"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column prop="deptName" label="单位/部门" width="260" />
      <!-- <el-table-column prop="orderNum" label="排序" width="200"></el-table-column> -->

      <el-table-column prop="status" align="center" label="唯一编码" width="150">
        <template slot-scope="scope">
          <el-tooltip content="点击复制" placement="top" effect="light">
            <span style="color:#6495ED;cursor:pointer" @click="copyText(scope.row.deptCode)">{{ scope.row.deptCode }}</span>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column prop="status" align="center" label="状态" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="200">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:dept:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            v-hasPermi="['system:dept:add']"
            size="mini"
            type="text"
            icon="el-icon-plus"
            @click="handleAdd(scope.row)"
          >新增</el-button>
          <el-button
            v-if="scope.row.parentId !== 0"
            v-hasPermi="['system:dept:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
          <el-button
            v-if="scope.row.deptId !== 100 && scope.row.parentId !== 0"
            v-hasPermi="['system:dept:edit']"
            size="mini"
            type="text"
            icon="el-icon-top"
            @click="handleMoveUp(scope.row)"
          >上移</el-button>
          <el-button
            v-if="scope.row.deptId !== 100 && scope.row.parentId !== 0"
            v-hasPermi="['system:dept:edit']"
            size="mini"
            type="text"
            icon="el-icon-bottom"
            @click="handleMoveDown(scope.row)"
          >下移</el-button>
          <el-button
            v-if="scope.row.deptId !== 100 && scope.row.parentId !== 0"
            v-hasPermi="['system:dept:edit']"
            size="mini"
            type="text"
            icon="el-icon-caret-top"
            @click="handleMoveTop(scope.row)"
          >置顶</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改单位/部门对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="780px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="padding-right:20px">
        <el-row>
          <el-col v-if="form.parentId !== 0" :span="24">
            <el-form-item label="上级单位/部门" prop="parentId">
              <treeselect
                v-model="form.parentId"
                :options="deptOptions"
                :normalizer="normalizer"
                placeholder="选择上级单位/部门"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="单位/部门" prop="deptName">
              <el-input v-model="form.deptName" placeholder="请输入单位/部门名称" />
            </el-form-item>
          </el-col>
          <!--
          <el-col :span="12" id="deptCodeSpan">
            <el-form-item label="" prop="deptCode">
              <el-input id="deptCodeKey" v-model="form.deptCode" style="width:164px;display:none"   />
            </el-form-item>
          </el-col>
          -->
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="负责人" prop="leader">
              <el-input v-model="form.leader" placeholder="请输入负责人" maxlength="20" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入联系电话" maxlength="11" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in dict.type.sys_normal_disable" :key="dict.value" :label="dict.value">{{
                  dict.label
                }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" style="padding-right:20px;padding-bottom:12px">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDept, getDept, delDept, addDept, updateDept, listDeptExcludeChild, changeDeptSort } from '@/api/system/dept'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  name: 'Dept',
  dicts: ['sys_normal_disable'],
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 原始单位/部门数据
      originalDeptList: [],
      // 表格树数据
      deptList: [],
      // 单位/部门树选项
      deptOptions: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部展开
      isExpandAll: true,
      // 重新渲染表格状态
      refreshTable: true,
      // 查询参数
      queryParams: {
        deptName: undefined,
        status: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        parentId: [
          { required: true, message: '上级单位/部门不能为空', trigger: 'blur' }
        ],
        deptName: [
          { required: true, message: '单位/部门名称不能为空', trigger: 'blur' }
        ],
        orderNum: [
          { required: true, message: '显示排序不能为空', trigger: 'blur' }
        ],
        email: [
          {
            type: 'email',
            message: '请输入正确的邮箱地址',
            trigger: ['blur', 'change']
          }
        ],
        phone: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: '请输入正确的手机号码',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询单位/部门列表 */
    getList() {
      this.loading = true
      listDept(this.queryParams).then(response => {
        this.originalDeptList = response.data
        this.deptList = this.handleTree(response.data, 'deptId')
        this.loading = false
      })
    },
    /** 转换单位/部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children
      }
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 复制文本
    copyText(spanText) {
      try {
        navigator.clipboard.writeText(spanText);
        this.$message({
          message: `编码 ${spanText} 复制成功`,
          type: 'success',
          duration: 2000
        });
      } catch (err) {
        this.$message.error('复制失败');
        console.error('复制失败', err);
      }
    },
    // 表单重置
    reset() {
      this.form = {
        deptId: undefined,
        parentId: undefined,
        deptName: undefined,
        orderNum: undefined,
        leader: undefined,
        phone: undefined,
        email: undefined,
        status: '0'
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      if (row !== undefined) {
        this.form.parentId = row.deptId
      }
      this.open = true
      this.title = '添加单位/部门'
      listDept().then(response => {
        this.deptOptions = this.handleTree(response.data, 'deptId')
      });
    },
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false
      this.isExpandAll = !this.isExpandAll
      this.$nextTick(() => {
        this.refreshTable = true
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      getDept(row.deptId).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改单位/部门'
        listDeptExcludeChild(row.deptId).then(response => {
          this.deptOptions = this.handleTree(response.data, 'deptId')
          //console.log(JSON.stringify(response.data));
          if (this.deptOptions.length === 0) {
            const noResultsOptions = { deptId: this.form.parentId, deptName: this.form.parentName, children: [] }
            this.deptOptions.push(noResultsOptions)
          }
        })
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.deptId !== undefined) {
            updateDept(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addDept(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除名称为"' + row.deptName + '"的数据项？').then(function() {
        return delDept(row.deptId)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => { })
    },
    /** 获取与指定单位/部门同级的所有单位/部门 */
    getSameLevelDepts(dept) {
      return this.originalDeptList.filter(d => d.parentId === dept.parentId)
    },
    /** 上移按钮操作 */
    handleMoveUp(row) {
      const siblings = this.getSameLevelDepts(row)
      // 按排序字段排序
      const sortedSiblings = siblings.sort((a, b) => a.orderNum - b.orderNum)
      const currentIndex = sortedSiblings.findIndex(item => item.deptId === row.deptId)

      if (currentIndex <= 0) {
        this.$modal.msg('已经是第一个，无法上移')
        return
      }

      const prevDept = sortedSiblings[currentIndex - 1]
      const currentSort = row.orderNum
      const prevSort = prevDept.orderNum

      changeDeptSort(row.deptId, prevSort).then(() => {
        changeDeptSort(prevDept.deptId, currentSort).then(() => {
          this.$modal.msgSuccess('上移成功')
          this.getList()
        })
      })
    },
    /** 下移按钮操作 */
    handleMoveDown(row) {
      const siblings = this.getSameLevelDepts(row)
      // 按排序字段排序
      const sortedSiblings = siblings.sort((a, b) => a.orderNum - b.orderNum)
      const currentIndex = sortedSiblings.findIndex(item => item.deptId === row.deptId)

      if (currentIndex < 0 || currentIndex >= sortedSiblings.length - 1) {
        this.$modal.msg('已经是最后一个，无法下移')
        return
      }

      const nextDept = sortedSiblings[currentIndex + 1]
      const currentSort = row.orderNum
      const nextSort = nextDept.orderNum

      changeDeptSort(row.deptId, nextSort).then(() => {
        changeDeptSort(nextDept.deptId, currentSort).then(() => {
          this.$modal.msgSuccess('下移成功')
          this.getList()
        })
      })
    },
    /** 置顶按钮操作 */
    handleMoveTop(row) {
      const siblings = this.getSameLevelDepts(row)
      const minSort = Math.min(...siblings.map(item => item.orderNum))

      if (row.orderNum <= minSort) {
        this.$modal.msg('该单位/部门已经在最顶部')
        return
      }

      changeDeptSort(row.deptId, minSort - 1).then(() => {
        this.$modal.msgSuccess('置顶成功')
        this.getList()
      })
    }
  }
}
</script>
