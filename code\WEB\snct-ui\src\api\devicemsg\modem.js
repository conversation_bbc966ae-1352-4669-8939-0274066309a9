import request from '@/utils/request'

// 查询Modem消息列表
export function listModem(query) {
  return request({
    url: '/devicemsg/modem/list',
    method: 'get',
    params: query
  })
}

// 查询Modem消息详细
export function getModem(id) {
  return request({
    url: '/devicemsg/modem/' + id,
    method: 'get'
  })
}

// 新增Modem消息
export function addModem(data) {
  return request({
    url: '/devicemsg/modem',
    method: 'post',
    data: data
  })
}

// 修改Modem消息
export function updateModem(data) {
  return request({
    url: '/devicemsg/modem',
    method: 'put',
    data: data
  })
}

// 删除Modem消息
export function delModem(id) {
  return request({
    url: '/devicemsg/modem/' + id,
    method: 'delete'
  })
}

// ================================================================
// 从HBase中获取Modem数据的接口
// ================================================================

// 分页查询Modem消息列表
export function listModemHbase(query) {
  return request({
    url: '/hbase/devicemsg/modem/query',
    method: 'get',
    params: query
  })
}

// 查询Modem消息详细
export function getModemHbase(rowKey) {
  return request({
    url: '/hbase/devicemsg/modem/detail/' + rowKey,
    method: 'get'
  })
}
