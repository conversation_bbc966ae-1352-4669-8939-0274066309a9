import request from '@/utils/request'

// 查询船舶预警日志列表
export function listWarning(query) {
  return request({
    url: '/business/warning/list',
    method: 'get',
    params: query
  })
}

// 查询船舶预警日志详细
export function getWarning(id) {
  return request({
    url: '/business/warning/' + id,
    method: 'get'
  })
}

// 新增船舶预警日志
export function addWarning(data) {
  return request({
    url: '/business/warning',
    method: 'post',
    data: data
  })
}

// 修改船舶预警日志
export function updateWarning(data) {
  return request({
    url: '/business/warning',
    method: 'put',
    data: data
  })
}

// 删除船舶预警日志
export function delWarning(id) {
  return request({
    url: '/business/warning/' + id,
    method: 'delete'
  })
}
