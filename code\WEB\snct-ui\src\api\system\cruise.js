import request from '@/utils/request'

// 查询航次列表
export function listCruise(query) {
  return request({
    url: '/business/cruise/list',
    method: 'get',
    params: query
  })
}

// 查询航次详细
export function getCruise(cruiseId) {
  return request({
    url: '/business/cruise/' + cruiseId,
    method: 'get'
  })
}

// 新增航次
export function addCruise(data) {
  return request({
    url: '/business/cruise',
    method: 'post',
    data: data
  })
}

// 修改航次
export function updateCruise(data) {
  return request({
    url: '/business/cruise',
    method: 'put',
    data: data
  })
}

// 删除航次
export function delCruise(cruiseId) {
  return request({
    url: '/business/cruise/' + cruiseId,
    method: 'delete'
  })
}

// 查询部门列表
export function listDept(query) {
  return request({
    url: '/system/dept/list',
    method: 'get',
    params: query
  })
}

// 查询部门列表（排除节点）
export function listDeptExcludeChild(deptId) {
  return request({
    url: '/system/dept/list/exclude/' + deptId,
    method: 'get'
  })
}

// 查询船只列表
export function getShipList(deptId) {
  return request({
    url: '/business/ship/listBy/'+deptId,
    method: 'get'
  })
}
