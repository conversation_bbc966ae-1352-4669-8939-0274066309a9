import request from '@/utils/request'

// 查询功放消息列表
export function listAmplifier(query) {
  return request({
    url: '/devicemsg/amplifier/list',
    method: 'get',
    params: query
  })
}

// 查询功放消息详细
export function getAmplifier(id) {
  return request({
    url: '/devicemsg/amplifier/' + id,
    method: 'get'
  })
}

// 新增功放消息
export function addAmplifier(data) {
  return request({
    url: '/devicemsg/amplifier',
    method: 'post',
    data: data
  })
}

// 修改功放消息
export function updateAmplifier(data) {
  return request({
    url: '/devicemsg/amplifier',
    method: 'put',
    data: data
  })
}

// 删除功放消息
export function delAmplifier(id) {
  return request({
    url: '/devicemsg/amplifier/' + id,
    method: 'delete'
  })
}

// ================================================================
// 从HBase中获取功放数据的接口
// ================================================================

// 分页查询功放设备数据列表
export function listAmplifierHbase(query) {
  return request({
    url: '/hbase/devicemsg/amplifier/query',
    method: 'get',
    params: query
  })
}

// 查询功放设备数据详情
export function getAmplifierHbase(rowKey) {
  return request({
    url: '/hbase/devicemsg/amplifier/detail/' + rowKey,
    method: 'get'
  })
}
