<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="78px">
      <el-form-item label="单位/部门" prop="deptName">
        <el-input
          v-model="queryParams.deptName"
          placeholder="请输入单位/部门名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="船舶名称" prop="shipName">
        <el-input
          v-model="queryParams.shipName"
          placeholder="请输入船舶名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!--
      <el-form-item label="衰减值" prop="decay">
        <el-input
          v-model="queryParams.decay"
          placeholder="请输入衰减值"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="温度" prop="temp">
        <el-input
          v-model="queryParams.temp"
          placeholder="请输入温度"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="输出功率" prop="outPower">
        <el-input
          v-model="queryParams.outPower"
          placeholder="请输入输出功率"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
    <!--
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:amplifier:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:amplifier:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:amplifier:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:amplifier:export']"
        >导出</el-button>
      </el-col>
      -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="amplifierList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="90" align="center">
        <template slot-scope="scope">
          <span>{{(queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1}}</span>
        </template>
      </el-table-column>
      <!--
      <el-table-column label="ID" align="center" prop="id" />
      -->
      <el-table-column label="单位/部门" align="center" prop="deptId" />
      <el-table-column label="船舶名称" align="center" prop="shipId" />
      <el-table-column label="衰减值" align="center" prop="decay" />
      <el-table-column label="温度" align="center" prop="temp" />
      <el-table-column label="输出功率" align="center" prop="outPower" />
      <el-table-column label="设备状态" align="center" prop="bucStatus" />
      <el-table-column label="采集时间" align="center" prop="createTime" width="160" />
      <!--
      <el-table-column label="状态 0默认 1发送云端成功 2发送云端失败" align="center" prop="status" />
      -->
      <!--
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:amplifier:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:amplifier:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
      -->
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改功放消息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="部门ID" prop="deptId">
          <el-input v-model="form.deptId" placeholder="请输入部门ID" />
        </el-form-item>
        <el-form-item label="船只id" prop="shipId">
          <el-input v-model="form.shipId" placeholder="请输入船只id" />
        </el-form-item>
        <el-form-item label="衰减值" prop="decay">
          <el-input v-model="form.decay" placeholder="请输入衰减值" />
        </el-form-item>
        <el-form-item label="温度" prop="temp">
          <el-input v-model="form.temp" placeholder="请输入温度" />
        </el-form-item>
        <el-form-item label="输出功率" prop="outPower">
          <el-input v-model="form.outPower" placeholder="请输入输出功率" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAmplifier, getAmplifier, delAmplifier, addAmplifier, updateAmplifier } from "@/api/devicemsg/amplifier";

export default {
  name: "Amplifier",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 功放消息表格数据
      amplifierList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deptId: null,
        deptName: null,
        shipId: null,
        shipName: null,
        decay: null,
        temp: null,
        outPower: null,
        bucStatus: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        deptId: [
          { required: true, message: "部门ID不能为空", trigger: "blur" }
        ],
        shipId: [
          { required: true, message: "船只id不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询功放消息列表 */
    getList() {
      this.loading = true;
      listAmplifier(this.queryParams).then(response => {
        this.amplifierList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        deptId: null,
        shipId: null,
        decay: null,
        temp: null,
        outPower: null,
        bucStatus: null,
        status: null,
        createTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加功放消息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getAmplifier(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改功放消息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateAmplifier(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAmplifier(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除功放消息编号为"' + ids + '"的数据项？').then(function() {
        return delAmplifier(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/amplifier/export', {
        ...this.queryParams
      }, `amplifier_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
