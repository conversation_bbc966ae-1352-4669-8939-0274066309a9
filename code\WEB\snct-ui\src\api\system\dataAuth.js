import request from '@/utils/request'

// 查询数据权限列表
export function listDataAuth(query) {
  return request({
    url: '/system/dataAuth/list',
    method: 'get',
    params: query
  })
}

// 查询数据权限详细
export function getDataAuth(dataAuthId) {
  return request({
    url: '/system/dataAuth/' + dataAuthId,
    method: 'get'
  })
}

// 新增数据权限
export function addDataAuth(data) {
  return request({
    url: '/system/dataAuth',
    method: 'post',
    data: data
  })
}

// 修改数据权限
export function updateDataAuth(data) {
  return request({
    url: '/system/dataAuth',
    method: 'put',
    data: data
  })
}

// 删除数据权限
export function delDataAuth(dataAuthId) {
  return request({
    url: '/system/dataAuth/' + dataAuthId,
    method: 'delete'
  })
}
